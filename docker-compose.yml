version: '3.8'

services:
  mysql:
    image: mysql:8.0.27
    container_name: hmall-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: hmall
      MYSQL_USER: hmall
      MYSQL_PASSWORD: 123456
      TZ: Asia/Shanghai
    ports:
      - "3307:3306"
    volumes:
      - ./mysql/conf/hm.cnf:/etc/mysql/conf.d/hm.cnf
      - ./mysql/init:/docker-entrypoint-initdb.d
      - mysql_data:/var/lib/mysql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - hmall-network

  nacos:
    image: nacos/nacos-server:v2.1.0-slim
    container_name: hmall-nacos
    restart: always
    depends_on:
      - mysql
    env_file:
      - ./nacos/custom.env
    ports:
      - "8848:8848"
      - "9848:9848"
      - "9849:9849"
    networks:
      - hmall-network

volumes:
  mysql_data:

networks:
  hmall-network:
    driver: bridge
