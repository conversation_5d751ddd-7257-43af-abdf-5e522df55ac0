server:
  port: 8085
spring:
  application:
    name: hm-pay-service
  profiles:
    active: local
  datasource:
    url: **************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: 123456
  cloud:
    nacos:
      server-addr: 127.0.0.1:8848
      discovery:
        namespace: dev
      config:
        namespace: dev
        file-extension: yaml
mybatis-plus:
  configuration:
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
  global-config:
    db-config:
      update-strategy: not_null
      id-type: auto
logging:
  level:
    com.hmall: debug
  pattern:
    dateformat: HH:mm:ss:SSS
  file:
    path: "logs/${spring.application.name}"
knife4j:
  enable: true
  openapi:
    title: 黑马商城支付服务接口文档
    description: "黑马商城支付服务接口文档"
    email: z<PERSON><PERSON><PERSON>@itcast.cn
    concat: 虎哥
    url: https://www.itcast.cn
    version: v1.0.0
    group:
      default:
        group-name: default
        api-rule: package
        api-rule-resources:
          - com.hmall.pay.controller
