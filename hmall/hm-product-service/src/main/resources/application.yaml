server:
  port: 8081
spring:
  application:
    name: hm-product-service
  profiles:
    active: local
  datasource:
    url: jdbc:mysql://${hm.db.host}:${hm.db.port:3306}/hm-item?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&serverTimezone=Asia/Shanghai
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: ${hm.db.pw}
  cloud:
    nacos:
      discovery:
        server-addr: ${hm.nacos.addr}
        namespace: ${hm.nacos.namespace}
      config:
        server-addr: ${hm.nacos.addr}
        namespace: ${hm.nacos.namespace}
        file-extension: yaml
mybatis-plus:
  configuration:
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
  global-config:
    db-config:
      update-strategy: NOT_NULL
      id-type: auto
logging:
  level:
    com.hmall.product: debug
  pattern:
    dateformat: HH:mm:ss:SSS
  file:
    path: "logs/${spring.application.name}"
knife4j:
  enable: true
  openapi:
    title: 商品管理服务接口文档
    description: "商品管理服务接口文档"
    email: <EMAIL>
    concat: 虎哥
    url: https://www.itcast.cn
    version: v1.0.0
    group:
      default:
        group-name: default
        api-rule: package
        api-rule-resources:
          - com.hmall.product.controller
